# 🐾 Cym702 Pet WebView

반려동물 건강 관리를 위한 Vue.js 기반 웹 애플리케이션

## 프로젝트 개요

Cym702 Pet WebView는 반려동물의 건강 상태를 모니터링하고 관리하기 위한 웹 애플리케이션입니다. 소변 검사 결과 분석, 체중 관리, 수분 섭취량 추적, 식이 관리 등 반려동물의 종합적인 건강 관리 기능을 제공하며, 모바일 환경에 최적화된 반응형 디자인을 갖추고 있습니다.

## 기술 스택

- **프레임워크**: Vue.js 2.6.11
- **UI 라이브러리**: Vuetify 2.4.0
- **상태 관리**: Vuex 3.6.2
- **라우팅**: Vue Router 3.2.0
- **HTTP 클라이언트**: Axios
- **다국어 지원**: Vue i18n 8.26.3
- **차트 시각화**: ApexCharts 3.35.3
- **빌드 도구**: Vue CLI 4.5.0
- **배포 환경**: AWS S3, CloudFront

## 주요 기능

- 반려동물 프로필 관리
- 소변 검사 결과 시각화 및 분석
- 체중, 수분 섭취량, 배뇨량 추적 및 차트 표시
- 건강 설문조사 및 결과 분석
- 다국어 지원 (한국어, 영어, 중국어, 일본어)
- 소셜 로그인 (카카오, 애플)
- 반응형 디자인 (모바일 최적화)
- PWA(Progressive Web App) 지원

## 빠른 시작

### 필수 요구사항

- Node.js v16 이상
- npm 또는 yarn

### 설치 및 실행

```bash
# node version 확인
node -v

# node version 변경
nvm use

# 의존성 설치
npm install
# 또는
yarn install

# 로컬 개발 서버 실행
npm run local
# 또는
npm run serve

# 개발 서버 실행
npm run dev

# 라이브 서버 실행
npm run live
```

### 빌드 및 배포

```bash
# 개발 환경 빌드
npm run build:dev

# 운영 환경 빌드
npm run build:prod

# 개발 환경 배포
npm run deploy-dev
npm run invalidate-dev

# 운영 환경 배포
npm run deploy
npm run invalidate
```

## 프로젝트 구조

```
src/
├── api/            # API 통신 모듈
├── assets/         # 정적 리소스(이미지, 폰트)
├── components/     # 재사용 가능한 컴포넌트
│   ├── Common/     # 공통 컴포넌트
│   ├── Home/       # 홈 화면 컴포넌트
│   ├── Analysis/   # 분석 화면 컴포넌트
│   └── Care/       # 케어 화면 컴포넌트
├── constants/      # 상수 및 유틸리티 함수
├── locales/        # 다국어 지원 파일
├── pages/          # 페이지 컴포넌트
├── plugins/        # Vue 플러그인 설정
├── router/         # 라우팅 설정
├── service/        # 서비스 모듈 (웹뷰, 네이티브 통신)
├── store/          # Vuex 상태 관리
├── styles/         # 전역 스타일
└── main.js         # 애플리케이션 진입점
```

## 환경 설정

프로젝트는 다양한 환경(로컬, 개발, 운영)에 대한 설정을 지원합니다:

```bash
# 환경 변수 파일
.env.local       # 로컬 개발 환경
.env.development # 개발 환경
.env.production  # 운영 환경
```

주요 환경 변수:

```
VUE_APP_BASE_URL="http://localhost:8080"
VUE_APP_API_URL="http://localhost:8000"
```

## API 엔드포인트

애플리케이션은 다음과 같은 주요 API 엔드포인트를 사용합니다:

- `/auth`: 인증 관련 API
- `/accounts`: 계정 관리 API
- `/subjects`: 반려동물 관리 API
- `/analysis`: 소변 검사 결과 분석 API
- `/cares`: 체중, 수분, 배뇨 관리 API
- `/solution`: 식이 솔루션 API
- `/surveys`: 건강 설문조사 API

## 개발 가이드

### 컴포넌트 개발

새로운 컴포넌트는 `src/components/` 디렉토리에 추가합니다:

```vue
<template>
  <div class="my-component">
    <!-- 컴포넌트 템플릿 -->
  </div>
</template>

<script>
export default {
  name: "MyComponent",
  props: {
    // 컴포넌트 속성
  },
  data() {
    return {
      // 컴포넌트 데이터
    };
  },
  methods: {
    // 컴포넌트 메서드
  },
};
</script>

<style scoped>
/* 컴포넌트 스타일 */
</style>
```

### API 통신

API 통신은 `src/api/` 디렉토리의 모듈을 사용합니다:

```javascript
// src/api/cym702/index.js
import { cym702 } from "../index";

export default {
  // API 메서드 정의
  getAnalysisData(subjectId, page, limit) {
    return cym702.get(`/analysis/score/${subjectId}`, {
      params: { page, limit, subjectId },
    });
  },
};
```

### 다국어 지원

다국어 지원은 `src/locales/` 디렉토리의 JSON 파일을 통해 관리됩니다:

```javascript
// 컴포넌트에서 사용
this.$t("message.hello");
```

## 배포 프로세스

### AWS S3 배포

```bash
# 개발 환경 배포
npm run build:dev
npm run deploy-dev

# 운영 환경 배포
npm run build:prod
npm run deploy
```

### CloudFront 캐시 무효화

```bash
# 개발 환경 캐시 무효화
npm run invalidate-dev

# 운영 환경 캐시 무효화
npm run invalidate
```

## 브라우저 지원

```
> 1%
last 2 versions
not dead
```

## PWA 지원

- 임시방편으로 만듦.
- `service-worker.js` 파일을 통해 서비스 워커 등록 (cache management)
- 오프라인 상태에서 `offline.html` 제공
- 홈 화면 추가 기능

## 문제 해결

### 일반적인 문제

- **API 연결 오류**: 환경 변수와 프록시 설정을 확인하세요.
- **빌드 오류**: Node.js 버전과 의존성을 확인하세요.
- **환경 변수 문제**: `.env` 파일이 올바르게 설정되어 있는지 확인하세요.

### 로그 확인

개발자 도구의 콘솔에서 로그를 확인하거나, 다음 코드를 추가하여 디버깅할 수 있습니다:

```javascript
console.log("환경 변수:", process.env.VUE_APP_API_URL);
```

## 성능 최적화

- 이미지 최적화 및 지연 로딩
- 코드 분할 및 지연 로딩
- 컴포넌트 메모이제이션
- 불필요한 렌더링 방지

## 기여 가이드

1. 저장소를 포크하고 기능 브랜치를 생성하세요.
2. 변경 사항을 커밋하고 테스트를 실행하세요.
3. 풀 리퀘스트를 제출하세요.

## 버전 관리

- Development 브랜치: 활발한 개발
- Main 브랜치: 프로덕션 준비 코드
- 버전 태그는 시맨틱 버저닝(X.Y.Z)을 따릅니다
- 라이브러리 버전 업그레이드 및 프레임워크 언어 변경(javascript -> typescript)
