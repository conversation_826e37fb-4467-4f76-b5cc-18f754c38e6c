<!DOCTYPE html>
<html lang="ko">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        .offline-container {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
            position: relative;
            z-index: 2;
        }

        .wifi-icon {
            font-size: 80px;
            margin-bottom: 30px;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: slideInDown 1s ease-out;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
            animation: slideInUp 1s ease-out 0.2s both;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            margin: 10px;
            animation: slideInUp 1s ease-out 0.4s both;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .status-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideInUp 1s ease-out 0.6s both;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 5px 0;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff6b6b;
            animation: blink 1.5s infinite;
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            width: 60px;
            height: 60px;
            background: white;
            transform: rotate(45deg);
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 30%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            bottom: 10%;
            right: 20%;
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            animation-delay: 1s;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.8;
            }

            50% {
                transform: scale(1.1);
                opacity: 0.6;
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes blink {

            0%,
            50% {
                opacity: 1;
            }

            51%,
            100% {
                opacity: 0.3;
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .offline-container {
                padding: 20px 15px;
            }

            .wifi-icon {
                font-size: 60px;
            }
        }
    </style>
</head>

<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="offline-container">
        <div class="wifi-icon">📶</div>
        <h1 class="title">Offline</h1>
        <p class="subtitle">
            Please check your Internet connection.<br>
            It will automatically retry when the connectionis restored.
        </p>

        <button class="retry-button" onclick="checkConnection()">Try again</button>
        <button class="retry-button" onclick="location.reload()">Refresh</button>

        <div class="status-info">
            <div class="status-item">
                <span>Network connection status</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div class="status-indicator"></div>
                    <span id="connection-status">offline</span>
                </div>
            </div>
            <div class="status-item">
                <span>Last check</span>
                <span id="last-check">just a moment ago</span>
            </div>
            <div class="status-item">
                <span>Automatic Retry</span>
                <span id="retry-counter">30 seconds later/span>
            </div>
        </div>
    </div>

    <script>
        let retryInterval;
        let countdownInterval;
        let retryCount = 30;

        function updateLastCheck() {
            document.getElementById('last-check').textContent = new Date().toLocaleTimeString('ko-KR');
        }

        function startCountdown() {
            retryCount = 30;
            countdownInterval = setInterval(() => {
                retryCount--;
                document.getElementById('retry-counter').textContent = `${retryCount}초 후`;

                if (retryCount <= 0) {
                    clearInterval(countdownInterval);
                    checkConnection();
                }
            }, 1000);
        }

        function checkConnection() {
            updateLastCheck();

            // 실제 연결 확인 시뮬레이션
            fetch('/', {
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 5000
            })
                .then(response => {
                    if (response.ok) {
                        document.getElementById('connection-status').textContent = 'online';
                        document.querySelector('.status-indicator').style.background = '#51cf66';
                        document.querySelector('.status-indicator').style.animation = 'none';

                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        throw new Error('Connection failed');
                    }
                })
                .catch(() => {
                    document.getElementById('connection-status').textContent = 'offline';
                    clearInterval(countdownInterval);
                    startCountdown();
                });
        }

        // 네트워크 상태 감지
        window.addEventListener('online', () => {
            document.getElementById('connection-status').textContent = 'online';
            document.querySelector('.status-indicator').style.background = '#51cf66';
            document.querySelector('.status-indicator').style.animation = 'none';
            setTimeout(() => location.reload(), 1000);
        });

        window.addEventListener('offline', () => {
            document.getElementById('connection-status').textContent = 'offline';
            document.querySelector('.status-indicator').style.background = '#ff6b6b';
            document.querySelector('.status-indicator').style.animation = 'blink 1.5s infinite';
        });

        // 페이지 로드 시 초기화
        window.addEventListener('load', () => {
            updateLastCheck();
            startCountdown();

            // 주기적으로 연결 상태 확인
            retryInterval = setInterval(checkConnection, 30000);
        });

        // 페이지 언로드 시 정리
        window.addEventListener('beforeunload', () => {
            clearInterval(retryInterval);
            clearInterval(countdownInterval);
        });
    </script>
</body>

</html>