<template>
  <div
    class="item"
    role="dialog"
    aria-modal="true"
    aria-labelledby="modalTitle"
    aria-describedby="modalDescription"
    @click.stop
  >
    <span class="content" :style="textLetterStyle" v-html="content" />
    <button class="confirm-button" @click.stop="$emit('handleConfirmClick')">
      {{ confirmText }}
    </button>
  </div>
</template>

<script>
export default {
  name: "ModalItem",

  /**
   * props
   *
   * @props content string,
   * @props confirmText string,
   * @props handleConfirmClick (e) => void,
   */
  props: {
    content: {
      type: String,
      default: "",
    },
    confirmText: {
      type: String,
      default: "",
    },
    handleConfirmClick: {
      type: Function,
      default: () => {},
    },
  },

  data() {
    return {};
  },

  computed: {
    /**
     *
     * @returns {String}
     */
    textLetterStyle() {
      const lang = (this.$i18n && this.$i18n.locale) || "ko";

      return {
        letterSpacing: lang === "ja" ? "-10%" : "-3%",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
$modal-background: #efefef;
$confirm-text-color: #41d8e6;
$content-color: #323232;

.item {
  width: calc(100% - 60px);
  min-height: 150px;
  max-height: 250px;
  background: $modal-background;
  border-radius: 10px;
  padding: 30px 18px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.content {
  color: $content-color;
  text-align: start;
  font-weight: 500;
  font-size: 18px;
  vertical-align: middle;
  margin-bottom: 25px;
  word-break: break-all;
}

.confirm-button {
  width: fit-content;
  height: fit-content;
  color: $confirm-text-color;
  font-weight: 800;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: -3%;
  text-align: right;
  vertical-align: middle;
  align-self: flex-end;
}
</style>
