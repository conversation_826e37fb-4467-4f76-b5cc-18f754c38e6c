<template>
  <ModalBackground @handleBackgroundClick="$emit('handleBackgroundClick')">
    <ModalItem
      :content="content"
      :confirmText="confirmText"
      @handleConfirmClick="$emit('handleConfirmClick')"
    />
  </ModalBackground>
</template>

<script>
import ModalBackground from "./modal-background.ui";
import ModalItem from "./modal-item.ui";

export default {
  name: "CommonModal",

  components: {
    ModalBackground,
    ModalItem,
  },

  /**
   * props
   *
   * @props content string
   * @props confirmText string
   * @props handleConfirmClick (e) => void(Function)
   * @props handleBackgroundClick (e) => void(Function)
   */
  props: {
    content: {
      type: String,
      default: "",
    },
    confirmText: {
      type: String,
      default: "",
    },
    handleConfirmClick: {
      type: Function,
      default: () => {},
    },
    handleBackgroundClick: {
      type: Function,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped></style>
