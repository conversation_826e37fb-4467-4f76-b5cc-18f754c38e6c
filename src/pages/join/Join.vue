<template>
  <div>
    <background>
      <!-- 회원가입 폼 헤더, 제목 영역 -->
      <FormHeader :currentStateIdx="currentStateIdx" :join="true" @moveBack="moveBack" />
      <div class="join-form__wrapper">
        <FormTitle :currentStateIdx="currentStateIdx" :join="true" />
        <FormSubTitle :currentStateIdx="currentStateIdx" :join="true" />
        <!-- 회원가입 폼, 상세정보입력 폼, 설문조사 폼 -->
        <component :is="currentForm" @nextPhaseHandler="nextPhaseHandler" />
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";

import FormHeader from "@/components/Join/FormHeader.vue";
import FormTitle from "@/components/Join/FormTitle.vue";
import FormSubTitle from "@/components/Join/FormSubTitle.vue";

import PhoneForm from "@/components/Forms/PhoneForm.vue";
import IdForm from "@/components/Forms/IdForm.vue";
import SignUpPwForm from "@/components/Forms/SignUpPwForm.vue";
import TermsForm from "@/components/Forms/TermsForm.vue";
import DetailForm from "@/components/Forms/UserDetailInfoForm.vue";

export default {
  components: {
    Background,
    FormHeader,
    FormTitle,
    FormSubTitle,
    PhoneForm,
    IdForm,
    SignUpPwForm,
    TermsForm,
    DetailForm,
  },
  data() {
    return {
      currentStateIdx: 3,
      formComponents: ["PhoneForm", "IdForm", "SignUpPwForm", "DetailForm", "TermsForm"],
    };
  },

  computed: {
    currentForm() {
      return this.formComponents[this.currentStateIdx];
    },
  },

  methods: {
    nextPhaseHandler(idx) {
      // console.log(idx);
      this.currentStateIdx = idx;
    },
    moveBack(idx) {
      this.currentStateIdx = this.currentStateIdx - idx;
    },
  },
};
</script>

<style lang="scss">
.join-form__wrapper {
  width: 100%;
  height: 100%;
  padding-top: 96px;
}
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 1.125em;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
  margin-top: 5px;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
