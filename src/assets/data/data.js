import {
  bloodTxt,
  glucoseTxt,
  proteinTxt,
  phTxt,
  ketoneTxt,
  newObj,
  explainations,
  bloodDetailInfo,
  glucoseDetailInfo,
  proteinDetailInfo,
  ketoneDetailInfo,
  phDetailInfo,
} from "../../constants/info.js";
import i18n from "../../i18n.js";
import store from "../../store"; // Vuex store import
// detail info: modal contents -> _modal
// txt -> _level

// Store에서 historyLevel 값을 가져오는 함수
const getHistoryLevel = () => {
  return store.state.cymHistory.historyLevel;
};

// Store의 historyLevel 값을 사용하는 예시 함수
const getProteinTextWithStoredLevel = (score) => {
  const storedLevel = getHistoryLevel();
  return proteinResultTxt(score, proteinTxt, storedLevel);
};

const cymScoreResultTxt = (score, obj) => {
  switch (score) {
    case "normal":
      return obj.normal;
    case "warning":
      return obj.warning;
    case "caution":
      return obj.caution;
    case "danger":
      return obj.danger;
    default:
      break;
  }
};

const glucoseResultTxt = (score, obj, level) => {
  console.log("glucoseResultTxt called with:", { score, level });

  switch (score) {
    case "normal": {
      // level이 없거나 0인 경우 goodMinus 반환
      if (!level || level === 0) {
        console.log("returning obj.goodMinus:", obj.goodMinus);
        return obj.goodMinus;
      }

      const txt = [obj.goodMinus, obj.goodPlusMinus];
      console.log("level:", level, "txt array:", txt);

      // level이 1이면 txt[0] (goodMinus), level이 2이면 txt[1] (goodPlusMinus)
      const result = txt[level - 1];
      console.log("returning result:", result);
      return result;
    }
    case "warning":
      return obj.warning;
    case "caution":
      return obj.caution;
    case "danger":
      return obj.danger;
    default:
      console.log("no matching score:", score);
      break;
  }
};

const proteinResultTxt = (score, obj, level) => {
  switch (score) {
    case "normal": {
      // level이 없거나 0인 경우 good 반환
      if (!level || level === 0) {
        return obj.good;
      }

      const txt = [obj.good, obj.normal];
      const result = txt[level - 1];
      return result;
    }
    case "warning":
      return obj.warning;
    case "caution":
      return obj.caution;
    case "danger": {
      const txt = [obj.danger, obj.dangerPlus];

      if (level === 5) return txt[0];
      else if (level === 6) return txt[1];
      return txt[0];
    }
    default:
      console.error("no matching score:", score);
      break;
  }
};

const ketoneResultTxt = (score, obj) => {
  switch (score) {
    case "normal":
      return obj.normal;
    case "caution_plus_minus":
      return obj.caution_plus_minus;
    case "caution_plus":
      return obj.caution_plus;
    case "warning":
      return obj.warning;
    case "danger":
      return obj.danger;
    default:
      break;
  }
};

const newCymScoreResultTxt = (score, obj, type) => {
  if (type === "nitrite") {
    switch (score) {
      case "normal":
        return obj.nitrite.normal;
      case "caution":
        return obj.nitrite.caution;
      default:
        break;
    }

    return i18n.t("nitrite_no_level");
  }

  const level = getHistoryLevel();

  switch (score) {
    case "good":
      return obj[type].good;
    case "normal":
      return obj[type].normal;
    case "warning": {
      if (type === "sg") {
        console.log("level:", level);
        if (level === 5) return obj[type].warning_plus;
      }

      return obj[type].warning;
    }
    case "caution":
      return obj[type].caution;
    case "danger":
      return obj[type].danger;
    default:
      console.error("no level", score, type);
      return i18n.t(`${type}_no_level`);
  }
};

function historyTypes(type, score) {
  switch (type) {
    case "blood":
      return cymScoreResultTxt(score, bloodTxt);
    case "glucose": {
      const level = getHistoryLevel();

      return glucoseResultTxt(score, glucoseTxt, level);
    }
    case "protein": {
      // level이 없으면 store에서 가져온 값 사용
      const finalLevel = getHistoryLevel();
      return proteinResultTxt(score, proteinTxt, finalLevel);
    }
    case "ph":
      return cymScoreResultTxt(score, phTxt);
    case "ketone":
      return ketoneResultTxt(score, ketoneTxt);
    default:
      return newCymScoreResultTxt(score, newObj, type);
  }
}

export {
  explainations,
  bloodDetailInfo,
  glucoseDetailInfo,
  proteinDetailInfo,
  ketoneDetailInfo,
  phDetailInfo,
  bloodTxt,
  glucoseTxt,
  proteinTxt,
  phTxt,
  ketoneTxt,
  historyTypes,
  getHistoryLevel,
  getProteinTextWithStoredLevel,
};
