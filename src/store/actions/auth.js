import base64 from "base-64";

import API from "../../api/auth/index";
import router from "../../router/index";
import store from "../../store/index";

export default {
  async LOGIN({ commit }, { account, password }) {
    try {
      const response = await API.fetchLoginRequest({
        account: account,
        password: password,
      });
      // localStorage.setItem("loginError", false);
      // console.log(response);
      store.commit("setLoginError", false);
      if (response.status === 201) {
        const token = response.data.accessToken; // access token
        const $REFRESH_TOKEN = response.data.refreshToken ?? ""; // refresh token
        const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        const decoded = base64.decode(payload);
        const { exp } = JSON.parse(decoded);
        localStorage.exp = exp;
        localStorage.setItem("subjectId", response.data.subjectId);
        localStorage.setItem("surveyStatus", response.data.status);
        localStorage.setItem("refresh", $REFRESH_TOKEN);
        // localStorage.setItem("loginError", false);

        /*global Webview*/
        /*eslint no-undef: "error"*/
        // 개발할때에는 주석처리.
        if (window.ReactNativeWebView && typeof Webview.loginSuccess !== undefined) {
          Webview.loginSuccess();
        }

        commit("LOGIN", token);
        router.push({ path: "/home" });
      }
    } catch (error) {
      // localStorage.setItem("loginError", true);
      store.commit("setLoginError", true);
      console.log(error);
      new Error("LOGIN request failed");
    }
  },

  async SNS_LOGIN({ commit }, { type, account }) {
    try {
      localStorage.setItem("loginError", false);
      const response = await API.fetchSnsLoginRequest(type, account);
      if (response.status === 201) {
        const token = response.data?.accessToken;
        // refresh token logic (운영에 배포가 안되어 주석처리 하였습니다.)
        const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        const decoded = atob(payload);
        const { exp } = JSON.parse(decoded);
        console.log(exp);
        localStorage.exp = exp;
        localStorage.setItem("subjectId", response.data.subjectId);
        localStorage.setItem("surveyStatus", response.data.status);
        localStorage.setItem("loginError", false);
        Webview.loginSuccess();
        commit("SNS_LOGIN", token);
        router.push({ path: "/home" });
      }
    } catch (error) {
      localStorage.setItem("loginError", true);
      console.log(error);
      new Error("SNSLOGIN request failed");
    }
  },

  SET_COOKIE({ commit }, token) {
    return commit("SET_COOKIE", token);
  },
};
