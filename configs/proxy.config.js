const getProxyConfig = (env) => {
  if (env === "production") {
    return {
      "/api": {
        target: "https://prod.yellosis.com/cym702-pet",
        changeOrigin: true,
      },
    };
  } else if (env === "development") {
    return {
      "/api": {
        target: "https://dev.yellosis.com/cym702-pet",
        changeOrigin: true,
      },
    };
  } else {
    // local
    return "https://dev.yellosis.com/cym702-pet";
  }
};

module.exports = getProxyConfig;
